<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/css/main.css">
    <title>Document</title>
</head>

<body>
    <section class="cashier-dash">
        <!-- Header Section -->
        <div class="pos-header">
            <div class="header-left">
                <div class="cashier-info">
                    <div class="cashier-avatar">
                        <img src="https://s7d1.scene7.com/is/image/wbcollab/sundar_pichai_google_ceo-1?qlt=90&fmt=webp&resMode=sharp2" alt="Admin">
                    </div>
                    <div class="cashier-details">
                        <div class="cashier-name">Admin</div>
                        <div class="cashier-date">2023-10-10</div>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="total-display">
                    <div class="total-label">Total</div>
                    <div class="total-amount" id="totalAmount">0</div>
                </div>
            </div>
        </div>

        <div class="container-custom">


            <!-- table -->
            <div class="scroll-table">
                <table class="table table-order table-hover align-middle">
                <thead>
                    <tr>
                        <th>Jml</th>
                        <th>Kode/Barcode</th>
                        <th>Nama Barang</th>
                        <th>Satuan</th>
                        <th>Harga @</th>
                        <th>Diskon %</th>
                        <th>Sub Total</th>
                    </tr>
                </thead>
                <tbody id="orderBody"></tbody>
            </table>
            </div>

        </div>
        <div class="pos-statusbar">
            <div class="left">Masukkan kode/barcode barang. Tekan [Enter] / [F12 / Double Klik untuk mencari barang].</div>
            <div class="right"><span>Admin</span><span id="statusDate"></span></div>
        </div>
        <div class="menu-cashier d-flex">
            <div class="p-2 flex-fill">
                <button id="btnAdd" class="btn btn-primary">F2. Tambah</button>
                <button id="btnEdit" class="btn btn-outline-primary">F3. Edit</button>
                <button id="btnPrint" class="btn btn-outline-secondary">F4. Cetak</button>
            </div>
            <div class="p-2 flex-fill">
                <button id="btnPay" class="btn btn-success">F5. Bayar</button>
                <button id="btnCancel" class="btn btn-warning">F6. Batal</button>
                <button id="btnPending" class="btn btn-info">F7. Pending</button>
            </div>
            <div class="p-2 flex-fill">
                <button id="btnExit" class="btn btn-danger">F8. Keluar</button>
            </div>
        </div>
    </section>
    <script>
        // Helper: parse number like "10.000" to 10000
        function parseRupiah(val){
            if(typeof val === 'number') return val;
            val = (val || '').toString().replace(/[^0-9]/g,'');
            return Number(val || 0);
        }
        function formatRupiah(num){
            return new Intl.NumberFormat('id-ID').format(num || 0);
        }

        const tbody = document.getElementById('orderBody');
        const totalEl = document.getElementById('totalAmount');
        const btnAdd = document.getElementById('btnAdd');
        const btnEdit = document.getElementById('btnEdit');

        let editMode = true; // allow inline edit by default

        function recalcRow(tr){
            const qty = parseRupiah(tr.cells[0].innerText);
            const price = parseRupiah(tr.cells[4].innerText);
            const disc = parseRupiah(tr.cells[5].innerText); // persen
            const subtotal = Math.round(qty * price * (1 - (disc/100)));
            tr.querySelector('.subtotal').innerText = formatRupiah(subtotal);
            return subtotal;
        }
        function recalcTotal(){
            let total = 0;
            [...tbody.rows].forEach(tr => total += recalcRow(tr));
            totalEl.innerText = formatRupiah(total);
        }
        function setRowEditable(tr, editable){
            for(let i of [0,1,2,3,4,5]){
                tr.cells[i].setAttribute('contenteditable', editable ? 'true' : 'false');
            }
        }
        function addRow(){
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td contenteditable="true">1</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true">1</td>
                <td contenteditable="true">0</td>
                <td contenteditable="true">0</td>
                <td class="subtotal">0</td>
            `;
            tbody.appendChild(tr);
            if(!editMode) setRowEditable(tr, false);
            tr.cells[1].focus();
            recalcTotal();
        }

        // Events
        tbody.addEventListener('input', (e)=>{
            const tr = e.target.closest('tr');
            if(!tr) return;
            recalcRow(tr);
            recalcTotal();
        });
        tbody.addEventListener('focusin', (e)=>{
            const td = e.target.closest('td');
            if(td) td.classList.add('editing');
        });
        tbody.addEventListener('focusout', (e)=>{
            const td = e.target.closest('td');
            if(td) td.classList.remove('editing');
        });

        btnAdd?.addEventListener('click', addRow);
        btnEdit?.addEventListener('click', ()=>{
            editMode = !editMode;
            [...tbody.rows].forEach(tr => setRowEditable(tr, editMode));
            btnEdit.classList.toggle('btn-outline-primary', editMode);
            btnEdit.classList.toggle('btn-primary', !editMode);
            btnEdit.textContent = editMode ? 'F3. Edit (ON)' : 'F3. Edit (OFF)';
        });

        // Helpers
        function findRowByBarcode(code){
            code = (code||'').trim();
            for(const tr of tbody.rows){
                if(tr.cells[1].innerText.trim() === code) return tr;
            }
            return null;
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e)=>{
            if(e.key === 'F2'){ e.preventDefault(); addRow(); }
            if(e.key === 'F3'){ e.preventDefault(); btnEdit?.click(); }
        });

        // Key handling inside table (Enter pada kolom barcode)
        tbody.addEventListener('keydown', (e)=>{
            if(e.key !== 'Enter') return;
            const td = e.target.closest('td');
            if(!td) return;
            e.preventDefault();
            const tr = td.parentElement;
            const col = td.cellIndex;
            if(col !== 1) return; // hanya saat di kolom barcode
            const code = td.innerText.trim();
            if(!code) return;
            const existing = findRowByBarcode(code);
            if(existing && existing !== tr){
                const qty = parseRupiah(existing.cells[0].innerText) + 1;
                existing.cells[0].innerText = qty;
                recalcRow(existing);
                td.innerText = '';
                td.focus();
            } else {
                if(!parseRupiah(tr.cells[0].innerText)) tr.cells[0].innerText = '1';
                if(!tr.cells[4].innerText.trim()) tr.cells[4].innerText = '0';
                recalcRow(tr);
                addRow();
            }
            recalcTotal();
        });

        // Initial state: buat satu baris kosong dan fokus barcode
        if(tbody.rows.length === 0){
            addRow();
        }
        (function(){
            const last = tbody.rows[tbody.rows.length-1];
            last?.cells[1]?.focus();
        })();

        // Initial calc
        recalcTotal();
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous">
    </script>
</body>

</html>