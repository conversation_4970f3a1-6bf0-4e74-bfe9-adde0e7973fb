<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/css/main.css">
    <title>Document</title>
</head>

<body>
    <section class="cashier-dash">

        <div class="container-custom">
            <div class="row">
                <div class="col-3 mr-auto">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="p-2 flex-grow-1">
                                    <img class="img-fluid"
                                        src="https://s7d1.scene7.com/is/image/wbcollab/sundar_pichai_google_ceo-1?qlt=90&fmt=webp&resMode=sharp2">
                                </div>
                                <div class="p-2">
                                    <h5>Kasir: Admin</h5>
                                    <h5>Tanggal: 2023-10-10</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-4 ml-auto total-payment">
                    <h4>Total:</h4>
                    <h1 id="totalAmount">0</h1>
                </div>
            </div>

            <!-- scan bar -->
            <div class="scan-bar card p-3 mb-2">
                <div class="input-group input-group-lg">
                    <span class="input-group-text bg-white border-end-0"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-upc-scan" viewBox="0 0 16 16"><path d="M1.5 1a.5.5 0 0 1 .5.5V3h1V1.5a.5.5 0 0 1 1 0V3h1V1.5a.5.5 0 0 1 1 0V3h1V1.5a.5.5 0 0 1 1 0V3h1V1.5a.5.5 0 0 1 1 0V3h1V1.5a.5.5 0 0 1 1 0V3h1V1.5a.5.5 0 0 1 1 0V3h.5a.5.5 0 0 1 0 1H15v1h.5a.5.5 0 0 1 0 1H15v1h.5a.5.5 0 0 1 0 1H15v1h.5a.5.5 0 0 1 0 1H15v1h.5a.5.5 0 0 1 0 1H15v1h.5a.5.5 0 0 1 0 1H15v.5a.5.5 0 0 1-1 0V15h-1v.5a.5.5 0 0 1-1 0V15h-1v.5a.5.5 0 0 1-1 0V15h-1v.5a.5.5 0 0 1-1 0V15h-1v.5a.5.5 0 0 1-1 0V15h-1v.5a.5.5 0 0 1-1 0V15H3v.5a.5.5 0 0 1-1 0V15h-.5a.5.5 0 0 1 0-1H2v-1h-.5a.5.5 0 0 1 0-1H2v-1h-.5a.5.5 0 0 1 0-1H2V8h-.5a.5.5 0 0 1 0-1H2V6h-.5a.5.5 0 0 1 0-1H2V4h-.5a.5.5 0 0 1 0-1H2V1.5a.5.5 0 0 1 .5-.5z"/></svg></span>
                    <input type="text" id="scanInput" class="form-control border-start-0" placeholder="Scan atau ketik kode/barcode lalu Enter..." autocomplete="off">
                    <button id="btnScanClear" class="btn btn-outline-secondary">Bersihkan</button>
                </div>
            </div>
            <!-- table -->
            <table class="table table-order table-hover align-middle">
                <thead>
                    <tr>
                        <th>Jml</th>
                        <th>Kode/Barcode</th>
                        <th>Nama Barang</th>
                        <th>Satuan</th>
                        <th>Harga @</th>
                        <th>Sub Total</th>
                    </tr>
                </thead>
                <tbody id="orderBody">
                    <tr>
                        <td contenteditable="true">1</td>
                        <td contenteditable="true">21sad</td>
                        <td contenteditable="true">Buku Gambar</td>
                        <td contenteditable="true">1</td>
                        <td contenteditable="true">10.000</td>
                        <td class="subtotal">10.000</td>
                    </tr>
                </tbody>
            </table>

        </div>
        <div class="menu-cashier d-flex">
            <div class="p-2 flex-fill">
                <button id="btnAdd" class="btn btn-primary">F2. Tambah</button>
                <button id="btnEdit" class="btn btn-outline-primary">F3. Edit</button>
                <button id="btnPrint" class="btn btn-outline-secondary">F4. Cetak</button>
            </div>
            <div class="p-2 flex-fill">
                <button id="btnPay" class="btn btn-success">F5. Bayar</button>
                <button id="btnCancel" class="btn btn-warning">F6. Batal</button>
                <button id="btnPending" class="btn btn-info">F7. Pending</button>
            </div>
            <div class="p-2 flex-fill">
                <button id="btnExit" class="btn btn-danger">F8. Keluar</button>
            </div>
        </div>
    </section>
    <script>
        // Helper: parse number like "10.000" to 10000
        function parseRupiah(val){
            if(typeof val === 'number') return val;
            val = (val || '').toString().replace(/[^0-9]/g,'');
            return Number(val || 0);
        }
        function formatRupiah(num){
            return new Intl.NumberFormat('id-ID').format(num || 0);
        }

        const tbody = document.getElementById('orderBody');
        const totalEl = document.getElementById('totalAmount');
        const btnAdd = document.getElementById('btnAdd');
        const btnEdit = document.getElementById('btnEdit');
        const scanInput = document.getElementById('scanInput');
        const btnScanClear = document.getElementById('btnScanClear');

        let editMode = true; // allow inline edit by default

        function recalcRow(tr){
            const qty = parseRupiah(tr.cells[0].innerText);
            const price = parseRupiah(tr.cells[4].innerText);
            const subtotal = qty * price;
            tr.querySelector('.subtotal').innerText = formatRupiah(subtotal);
            return subtotal;
        }
        function recalcTotal(){
            let total = 0;
            [...tbody.rows].forEach(tr => total += recalcRow(tr));
            totalEl.innerText = formatRupiah(total);
        }
        function setRowEditable(tr, editable){
            for(let i of [0,1,2,3,4]){
                tr.cells[i].setAttribute('contenteditable', editable ? 'true' : 'false');
            }
        }
        function addRow(){
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td contenteditable="true">1</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true">1</td>
                <td contenteditable="true">0</td>
                <td class="subtotal">0</td>
            `;
            tbody.appendChild(tr);
            if(!editMode) setRowEditable(tr, false);
            tr.cells[1].focus();
            recalcTotal();
        }

        // Events
        tbody.addEventListener('input', (e)=>{
            const tr = e.target.closest('tr');
            if(!tr) return;
            recalcRow(tr);
            recalcTotal();
        });
        tbody.addEventListener('focusin', (e)=>{
            const td = e.target.closest('td');
            if(td) td.classList.add('editing');
        });
        tbody.addEventListener('focusout', (e)=>{
            const td = e.target.closest('td');
            if(td) td.classList.remove('editing');
        });

        btnAdd?.addEventListener('click', addRow);
        btnEdit?.addEventListener('click', ()=>{
            editMode = !editMode;
            [...tbody.rows].forEach(tr => setRowEditable(tr, editMode));
            btnEdit.classList.toggle('btn-outline-primary', editMode);
            btnEdit.classList.toggle('btn-primary', !editMode);
            btnEdit.textContent = editMode ? 'F3. Edit (ON)' : 'F3. Edit (OFF)';
        });

        // Scan handling
        function findRowByBarcode(code){
            code = (code||'').trim();
            for(const tr of tbody.rows){
                if(tr.cells[1].innerText.trim() === code) return tr;
            }
            return null;
        }
        function addOrIncrementByBarcode(code){
            const existing = findRowByBarcode(code);
            if(existing){
                const qty = parseRupiah(existing.cells[0].innerText) + 1;
                existing.cells[0].innerText = qty;
                recalcRow(existing);
            } else {
                addRow();
                const last = tbody.rows[tbody.rows.length-1];
                last.cells[1].innerText = code;
                last.cells[2].innerText = 'Item '+code; // placeholder nama
                last.cells[4].innerText = '0'; // placeholder harga
                recalcRow(last);
            }
            recalcTotal();
        }
        scanInput?.addEventListener('keydown', (e)=>{
            if(e.key === 'Enter'){
                e.preventDefault();
                const code = scanInput.value.trim();
                if(code){ addOrIncrementByBarcode(code); scanInput.value=''; }
            }
        });
        btnScanClear?.addEventListener('click', ()=>{ scanInput.value=''; scanInput.focus(); });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e)=>{
            if(e.key === 'F2'){ e.preventDefault(); addRow(); }
            if(e.key === 'F3'){ e.preventDefault(); btnEdit?.click(); }
            if(e.key === 'F8'){ e.preventDefault(); scanInput?.focus(); }
        });

        // Initial calc
        recalcTotal();
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous">
    </script>
</body>

</html>