<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link rel="stylesheet" href="assets/css/main.css">
    <title>Document</title>
</head>

<body>
    <!-- Top Header Bar -->
    <div class="top-header">
        <div class="header-left">
            <span class="app-title">🛒 Modern POS</span>
        </div>
        <div class="header-right">
            <span class="current-time" id="currentTime">17:03:12</span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Left Panel - Info Kasir -->
        <div class="left-panel">
            <div class="cashier-info">
                <div class="info-row">
                    <label>Kasir</label>
                    <input type="text" value="Admin" readonly>
                </div>
                <div class="info-row">
                    <label>Tanggal</label>
                    <input type="text" value="17-03-2012" readonly>
                </div>
                <div class="info-row">
                    <label>Pelanggan</label>
                    <div class="customer-input">
                        <input type="text" placeholder="Tunai">
                        <button class="btn-search">🔍</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Total -->
        <div class="right-panel">
            <div class="total-display">
                <div class="total-label">Total :</div>
                <div class="total-amount" id="totalAmount">0</div>
            </div>
        </div>

        <!-- Center Panel - Table -->
        <div class="center-panel">
            <div class="table-container">
                <table class="pos-table">
                    <thead>
                        <tr>
                            <th>Jml</th>
                            <th>Kode/Barcode</th>
                            <th>Nama Barang</th>
                            <th>Satuan</th>
                            <th>Harga @</th>
                            <th>Diskon %</th>
                            <th>Sub Total</th>
                        </tr>
                    </thead>
                    <tbody id="orderBody">
                        <tr class="input-row">
                            <td>1</td>
                            <td><input type="text" class="barcode-input" placeholder="Scan/ketik barcode..."></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>0</td>
                            <td>0</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Bottom Menu Bar -->
    <div class="bottom-menu">
        <button class="menu-btn" id="btnAdd">F1. Tambah</button>
        <button class="menu-btn" id="btnEdit">F3. Edit</button>
        <button class="menu-btn" id="btnDelete">F4. Cetak</button>
        <button class="menu-btn" id="btnPay">F5. Bayar</button>
        <button class="menu-btn" id="btnCancel">F6. Batal</button>
        <button class="menu-btn" id="btnPending">F7. Pending</button>
        <button class="menu-btn" id="btnExit">F8. Keluar</button>
        <div class="menu-right">
            <span class="user-info">Admin</span>
            <span class="date-info">17/03/12</span>
        </div>
    </div>
    <script>
        // Update time
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('id-ID', { hour12: false });
            document.getElementById('currentTime').textContent = timeStr;
        }
        setInterval(updateTime, 1000);
        updateTime();

        // Helper functions
        function parseRupiah(val) {
            if (typeof val === 'number') return val;
            val = (val || '').toString().replace(/[^0-9]/g, '');
            return Number(val || 0);
        }

        function formatRupiah(num) {
            return new Intl.NumberFormat('id-ID').format(num || 0);
        }

        const tbody = document.getElementById('orderBody');
        const totalEl = document.getElementById('totalAmount');
        const barcodeInput = document.querySelector('.barcode-input');

        // Sample product database
        const products = {
            '123456': { name: 'Buku Tulis', price: 5000, unit: 'Pcs' },
            '789012': { name: 'Pulpen Biru', price: 3000, unit: 'Pcs' },
            '345678': { name: 'Penghapus', price: 2000, unit: 'Pcs' },
            '21sad': { name: 'Buku Gambar', price: 10000, unit: 'Pcs' }
        };

        function addNewRow() {
            const newRow = document.createElement('tr');
            newRow.className = 'input-row';
            newRow.innerHTML = `
                <td>1</td>
                <td><input type="text" class="barcode-input" placeholder="Scan/ketik barcode..."></td>
                <td></td>
                <td></td>
                <td></td>
                <td>0</td>
                <td>0</td>
            `;
            tbody.appendChild(newRow);

            // Focus on new barcode input
            const newInput = newRow.querySelector('.barcode-input');
            newInput.focus();

            // Add event listener to new input
            newInput.addEventListener('keydown', handleBarcodeInput);
        }

        function handleBarcodeInput(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const input = e.target;
                const barcode = input.value.trim();

                if (!barcode) return;

                const row = input.closest('tr');
                const product = products[barcode];

                if (product) {
                    // Fill product info
                    row.cells[2].textContent = product.name;
                    row.cells[3].textContent = product.unit;
                    row.cells[4].textContent = formatRupiah(product.price);

                    // Calculate subtotal
                    const qty = parseInt(row.cells[0].textContent) || 1;
                    const subtotal = qty * product.price;
                    row.cells[6].textContent = formatRupiah(subtotal);

                    // Replace input with text
                    const cell = input.parentElement;
                    cell.textContent = barcode;

                    // Add new row and calculate total
                    addNewRow();
                    calculateTotal();
                } else {
                    alert('Produk tidak ditemukan!');
                    input.select();
                }
            }
        }

        function calculateTotal() {
            let total = 0;
            const rows = tbody.querySelectorAll('tr:not(.input-row)');

            rows.forEach(row => {
                const subtotalText = row.cells[6].textContent;
                const subtotal = parseRupiah(subtotalText);
                total += subtotal;
            });

            totalEl.textContent = formatRupiah(total);
        }

        // Initialize
        barcodeInput.addEventListener('keydown', handleBarcodeInput);
        barcodeInput.focus();

        // Menu button handlers
        document.getElementById('btnAdd').addEventListener('click', addNewRow);
        document.getElementById('btnPay').addEventListener('click', () => {
            alert('Fitur Bayar - Total: Rp ' + totalEl.textContent);
        });
        document.getElementById('btnCancel').addEventListener('click', () => {
            if (confirm('Batalkan transaksi?')) {
                tbody.innerHTML = `
                    <tr class="input-row">
                        <td>1</td>
                        <td><input type="text" class="barcode-input" placeholder="Scan/ketik barcode..."></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>0</td>
                        <td>0</td>
                    </tr>
                `;
                const newInput = tbody.querySelector('.barcode-input');
                newInput.addEventListener('keydown', handleBarcodeInput);
                newInput.focus();
                calculateTotal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F1') { e.preventDefault(); addNewRow(); }
            if (e.key === 'F5') { e.preventDefault(); document.getElementById('btnPay').click(); }
            if (e.key === 'F6') { e.preventDefault(); document.getElementById('btnCancel').click(); }
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous">
    </script>
</body>

</html>