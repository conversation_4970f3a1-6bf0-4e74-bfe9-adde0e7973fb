/* Modern POS Application Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f0f2f5;
    height: 100vh;
    overflow: hidden;
}

/* Top Header */
.top-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
}

.app-title {
    font-size: 16px;
    font-weight: 600;
}

.current-time {
    font-size: 14px;
    font-weight: 500;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 250px 1fr 200px;
    grid-template-rows: 1fr;
    height: calc(100vh - 100px);
    gap: 1px;
    background: #ddd;
}

/* Left Panel - Cashier Info */
.left-panel {
    background: #ffffff;
    padding: 16px;
}

.cashier-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-row {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-row label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
}

.info-row input {
    padding: 6px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    background: #f9f9f9;
}

.customer-input {
    display: flex;
    gap: 4px;
}

.customer-input input {
    flex: 1;
}

.btn-search {
    padding: 6px 10px;
    border: 1px solid #ccc;
    background: #f0f0f0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.btn-search:hover {
    background: #e0e0e0;
}

/* Right Panel - Total */
.right-panel {
    background: #ffffff;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.total-display {
    text-align: center;
}

.total-label {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
}

.total-amount {
    font-size: 32px;
    font-weight: 700;
    color: #2c5aa0;
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Center Panel - Table */
.center-panel {
    background: #ffffff;
    display: flex;
    flex-direction: column;
}

.table-container {
    flex: 1;
    overflow: auto;
}

.pos-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.pos-table thead {
    background: #4a90e2;
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

.pos-table th {
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    font-size: 13px;
    border-right: 1px solid rgba(255,255,255,0.2);
}

.pos-table th:last-child {
    border-right: none;
}

.pos-table td {
    padding: 8px;
    border-bottom: 1px solid #eee;
    border-right: 1px solid #eee;
    vertical-align: middle;
}

.pos-table td:last-child {
    border-right: none;
}

.pos-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.pos-table tbody tr:hover {
    background: #e3f2fd;
}

/* Input row styling */
.input-row {
    background: #fff3cd !important;
}

.input-row:hover {
    background: #fff3cd !important;
}

.barcode-input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 4px;
    font-size: 14px;
    outline: none;
}

.barcode-input:focus {
    background: white;
    border: 2px solid #4a90e2;
    border-radius: 4px;
}

/* Numeric columns alignment */
.pos-table td:nth-child(1),
.pos-table td:nth-child(5),
.pos-table td:nth-child(6),
.pos-table td:nth-child(7) {
    text-align: right;
}

.pos-table td:nth-child(4) {
    text-align: center;
}

/* Bottom Menu */
.bottom-menu {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
    height: 50px;
}

.menu-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.menu-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.menu-btn:active {
    transform: translateY(0);
}

.menu-right {
    margin-left: auto;
    display: flex;
    gap: 16px;
    color: white;
    font-size: 12px;
}

.user-info, .date-info {
    background: rgba(255,255,255,0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 200px 1fr 180px;
    }

    .total-amount {
        font-size: 24px;
        min-height: 60px;
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto 1fr;
    }

    .left-panel, .right-panel {
        padding: 12px;
    }

    .cashier-info {
        flex-direction: row;
        gap: 16px;
    }

    .total-display {
        text-align: left;
    }

    .menu-right {
        display: none;
    }
}