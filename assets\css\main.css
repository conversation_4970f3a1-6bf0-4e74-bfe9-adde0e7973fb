.container-custom {
    margin: 10px 30px;
}

.container-custom .ml-auto {
    margin-left: auto;
}

.container-custom .mr-auto {
    margin-right: auto;
}

.cashier-dash .total-payment {
    text-align: right;
}

.cashier-dash .table-order {
    border: 1px solid #ccc;
}

.cashier-dash .menu-cashier {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #0d6efd;
    color: #fff;
    padding: 12px 16px;
    gap: 8px;
    z-index: 1000;
}

.cashier-dash {
    padding-bottom: 130px;
}

.table-order th,
.table-order td {
    vertical-align: middle;
}

.table-order tbody tr td[contenteditable="true"] {
    outline: none;
    background: #fffdfa;
}

.table-order tbody tr td.editing {
    box-shadow: inset 0 0 0 2px #ffec99;
}

.total-payment h1 {
    font-weight: 700;
}

.menu-cashier .btn {
    min-width: 120px;
    margin-right: 8px;
}

@media (max-width: 576px) {
    .menu-cashier .btn {
        min-width: auto;
        margin: 4px;
    }
}

/* --- Enhanced visual design --- */
:root {
    --brand: #0d6efd;
    --bg: #f6f8fb;
    --text: #1f2937;
    --muted: #64748b;
    --surface: #ffffff;
    --border: #e5e7eb;
}

body,
.cashier-dash {
    background: var(--bg);
    color: var(--text);
}

.container-custom {
    margin: 16px auto 30px;
    padding: 0 16px;
}

.card {
    border: none;
    border-radius: 12px;
    background: var(--surface);
    box-shadow: 0 4px 16px rgba(2, 6, 23, 0.06);
}

.total-payment h4 {
    color: var(--muted);
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.08em;
    margin-bottom: 4px;
}

.total-payment h1 {
    color: var(--brand);
    font-weight: 800;
    line-height: 1;
    font-size: clamp(28px, 4vw, 48px);
}

.table-order {
    margin-top: 24px;
    border: 1px solid var(--border);
    border-radius: 12px;
    overflow: hidden;
}

.table-order th,
.table-order td {
    padding: 0.9rem 1rem;
}

.table-order thead th {
    background: #f1f5f9;
    position: sticky;
    top: 0;
    z-index: 1;
}

.table-order tbody tr:nth-child(even) {
    background: #fafbfc;
}

.table-order tbody tr:hover {
    background: #eef2ff;
}

.table-order td:nth-child(1),
.table-order td:nth-child(5),
.table-order td:nth-child(6) {
    text-align: right;
}

.table-order td:nth-child(4) {
    text-align: center;
}

.table-order td:nth-child(2) {
    font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace;
    letter-spacing: 0.3px;
    color: #334155;
}

.table-order tbody tr td[contenteditable="true"] {
    background: #fffdfa;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.table-order tbody tr td.editing {
    box-shadow: inset 0 0 0 2px #ffec99, 0 0 0 9999px rgba(255, 255, 255, 0);
}

.menu-cashier {
    background: #fdff86;
    padding-bottom: calc(12px + env(safe-area-inset-bottom));
    box-shadow: 0 -6px 24px rgba(2, 6, 23, 0.12);
    backdrop-filter: saturate(1.1) blur(4px);
}

.menu-cashier .p-2.flex-fill {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.menu-cashier .btn {
    border-radius: 999px;
    padding: 10px 16px;
    font-weight: 600;
}

.menu-cashier .btn-primary,
.menu-cashier .btn-outline-primary:hover {
    box-shadow: 0 8px 20px rgba(255, 211, 115, 0.35);
}

.menu-cashier .btn:hover {
    transform: translateY(-1px);
}

.menu-cashier .btn:active {
    transform: translateY(0);
}


/* Scan bar styling */
.scan-bar .input-group {
    border: 1px solid var(--border);
    border-radius: 12px;
    overflow: hidden;
}

.scan-bar .input-group-text {
    border: none;
    color: #475569;
}

.scan-bar .form-control {
    border: none;
    box-shadow: none;
}

.scan-bar .btn {
    border-radius: 0;
}


.scroll-table {
    overflow-y: auto;
    height: 400px;
    margin-top: 50px;
}


body {
    overflow: hidden;
    height: 100vh;
}

.head-cashier {
    .img-fluid {
        width: 100px;
    }

    .img-fluid {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 10px;
        border: 2px solid #000000;
    }
}