.container-custom {
    margin: 10px 30px;
}

.container-custom .ml-auto {
    margin-left: auto;
}

.container-custom .mr-auto {
    margin-right: auto;
}

.cashier-dash .total-payment {
    text-align: right;
}

.cashier-dash .table-order {
    border: 1px solid #ccc;
}

.cashier-dash .menu-cashier {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #0d6efd;
    color: #fff;
    padding: 12px 16px;
    gap: 8px;
    z-index: 1000;
}

.cashier-dash {
    padding-bottom: 130px;
}

.table-order th,
.table-order td {
    vertical-align: middle;
}

.table-order tbody tr td[contenteditable="true"] {
    outline: none;
    background: #fffdfa;
}

.table-order tbody tr td.editing {
    box-shadow: inset 0 0 0 2px #ffec99;
}

.total-payment h1 {
    font-weight: 700;
}

.menu-cashier .btn {
    min-width: 120px;
    margin-right: 8px;
}

@media (max-width: 576px) {
    .menu-cashier .btn {
        min-width: auto;
        margin: 4px;
    }
}

/* --- Modern POS Color Palette --- */
:root {
    --primary: #6366f1;
    --primary-light: #a5b4fc;
    --secondary: #10b981;
    --accent: #f59e0b;
    --danger: #ef4444;
    --warning: #f97316;
    --info: #06b6d4;
    --bg: #f8fafc;
    --surface: #ffffff;
    --surface-alt: #f1f5f9;
    --text: #0f172a;
    --text-muted: #64748b;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --shadow: rgba(15, 23, 42, 0.08);
}

/* Base Styles */
body {
    background: var(--bg);
    color: var(--text);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
    height: 100vh;
    margin: 0;
}

.cashier-dash {
    height: 100vh;
    display: flex;
    flex-direction: column;
}
/* Header Section */
.pos-header {
    background: linear-gradient(135deg, var(--surface) 0%, var(--surface-alt) 100%);
    border-bottom: 1px solid var(--border);
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px var(--shadow);
}

.header-left .cashier-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cashier-avatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid var(--primary-light);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

.cashier-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cashier-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.cashier-name {
    font-weight: 600;
    font-size: 16px;
    color: var(--text);
}

.cashier-date {
    font-size: 14px;
    color: var(--text-muted);
}

.header-right .total-display {
    text-align: right;
}

.total-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.total-amount {
    font-size: 32px;
    font-weight: 800;
    color: var(--primary);
    line-height: 1;
    font-variant-numeric: tabular-nums;
}

.container-custom {
    flex: 1;
    padding: 16px 24px;
    overflow: hidden;
}

.card {
    border: none;
    border-radius: 12px;
    background: var(--surface);
    box-shadow: 0 4px 16px rgba(2, 6, 23, 0.06);
}

.total-payment h4 {
    color: var(--muted);
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.08em;
    margin-bottom: 4px;
}

.total-payment h1 {
    color: var(--brand);
    font-weight: 800;
    line-height: 1;
    font-size: clamp(28px, 4vw, 48px);
}

.table-order {
    margin-top: 24px;
    border: 1px solid var(--border);
    border-radius: 12px;
    overflow: hidden;
}

.table-order th,
.table-order td {
    padding: 0.9rem 1rem;
}

.table-order thead th {
    background: linear-gradient(135deg, var(--primary) 0%, #5b21b6 100%);
    color: white;
    position: sticky;
    top: 0;
    z-index: 5;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    padding: 16px 12px;
}
.table-order thead.stuck th{
    box-shadow: 0 4px 16px rgba(99, 102, 241, 0.25);
}

.table-order tbody tr:nth-child(even) {
    background: var(--surface-alt);
}

.table-order tbody tr:hover {
    background: rgba(99, 102, 241, 0.05);
}

.table-order td:nth-child(1),
.table-order td:nth-child(5),
.table-order td:nth-child(6),
.table-order td:nth-child(7) {
    text-align: right;
    font-variant-numeric: tabular-nums;
}

.table-order td:nth-child(4) {
    text-align: center;
}

.table-order td:nth-child(2) {
    font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace;
    letter-spacing: 0.3px;
    color: #334155;
}

.table-order tbody tr td[contenteditable="true"] {
    background: var(--surface);
    transition: all 0.2s ease;
    cursor: text;
}

.table-order tbody tr td.editing {
    background: rgba(99, 102, 241, 0.08);
    box-shadow: inset 0 0 0 2px var(--primary-light);
    outline: none;
}

.menu-cashier {
    background: var(--surface);
    border-top: 1px solid var(--border);
    padding: 16px 24px;
    box-shadow: 0 -4px 16px var(--shadow);
    backdrop-filter: blur(8px);
}

.menu-cashier .p-2.flex-fill {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.menu-cashier .btn {
    border-radius: 12px;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 14px;
    border: none;
    transition: all 0.2s ease;
    min-width: 120px;
}

.menu-cashier .btn-primary {
    background: var(--primary);
    color: white;
}
.menu-cashier .btn-primary:hover {
    background: #5b21b6;
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.menu-cashier .btn-success {
    background: var(--secondary);
    color: white;
}
.menu-cashier .btn-success:hover {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

.menu-cashier .btn-warning {
    background: var(--warning);
    color: white;
}
.menu-cashier .btn-warning:hover {
    background: #ea580c;
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(249, 115, 22, 0.3);
}

.menu-cashier .btn-danger {
    background: var(--danger);
    color: white;
}
.menu-cashier .btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(239, 68, 68, 0.3);
}

.menu-cashier .btn-info {
    background: var(--info);
    color: white;
}
.menu-cashier .btn-info:hover {
    background: #0891b2;
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(6, 182, 212, 0.3);
}

.menu-cashier .btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
}
.menu-cashier .btn-outline-primary:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-1px);
}

.menu-cashier .btn-outline-secondary {
    background: transparent;
    border: 2px solid var(--text-muted);
    color: var(--text-muted);
}
.menu-cashier .btn-outline-secondary:hover {
    background: var(--text-muted);
    color: white;
    transform: translateY(-1px);
}

.menu-cashier .btn:hover {
    transform: translateY(-1px);
}

.menu-cashier .btn:active {
    transform: translateY(0);
}


/* Scan bar styling */
.scan-bar .input-group {
    border: 1px solid var(--border);
    border-radius: 12px;
    overflow: hidden;
}

.scan-bar .input-group-text {
    border: none;
    color: #475569;
}

.scan-bar .form-control {
    border: none;
    box-shadow: none;
}

.scan-bar .btn {
    border-radius: 0;
}


.scroll-table {
    height: 100%;
    overflow-y: auto;
    border-radius: 16px;
    background: var(--surface);
    border: 1px solid var(--border);
    box-shadow: 0 4px 16px var(--shadow);
}


body {
    overflow: hidden;
    height: 100vh;
}

.head-cashier .img-fluid{
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid #000000;
}

/* POS-like bars */
.pos-menubar{
  position: sticky; top: 0; z-index: 1100;
  display:flex; gap:16px; align-items:center;
  background: linear-gradient(#1f4aa8,#183b85);
  color:#eaf2ff; padding:8px 16px; border-bottom:1px solid rgba(255,255,255,.15);
  font-weight:600; letter-spacing:.3px;
}
.pos-menubar span{cursor:default; opacity:.95}

.pos-statusbar{
  background: var(--surface-alt);
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  font-size: 14px;
  color: var(--text-muted);
}
.pos-statusbar .right{
  display: flex;
  gap: 16px;
  font-weight: 500;
}

/* POS table skin */
.pos-table-container{ margin-top: 8px; border:1px solid var(--border); border-radius:10px; overflow:hidden; background: #fff; }
.pos-table{ width:100%; border-collapse:separate; border-spacing:0; }
.pos-table thead th{ background: #1f4aa8; color:#fff; padding:.7rem .8rem; font-weight:700; }
.pos-table tbody td{ padding:.65rem .8rem; }
.pos-table tbody tr:nth-child(even){ background:#f8fafc; }
.pos-table tbody tr:hover{ background:#eef2ff; }
.pos-table td:nth-child(1), .pos-table td:nth-child(5), .pos-table td:nth-child(6), .pos-table td:nth-child(7){ text-align:right; }
.pos-table td:nth-child(4){ text-align:center; }

/* Keep existing bootstrap table styles working */
.table.table-order{ border:none; margin:0; }
.scroll-table{ height: calc(100vh - 280px); margin-top: 10px; }

/* Adapt header block to look app-like */
.head-cashier .card{ background:#eef4ff; border:1px solid #c7d2fe; border-radius: 12px; }
.head-cashier .card-body{ padding: 10px 12px; }
.head-cashier .img-fluid{ width: 72px; height: 72px; border-radius: 8px; object-fit: cover; }
.head-cashier h5{ margin: 2px 0; font-weight: 700; }
.total-payment h4{ color:#475569; font-weight:700; letter-spacing:.06em; text-transform:uppercase; }
.total-payment h1{ color:#0b2f97; font-size: clamp(32px, 5vw, 56px); font-weight:800; line-height:1; }

