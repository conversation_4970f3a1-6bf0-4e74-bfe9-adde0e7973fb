.container-custom {
    margin: 10px 30px;
}

.container-custom .ml-auto {
    margin-left: auto;
}

.container-custom .mr-auto {
    margin-right: auto;
}

.cashier-dash .total-payment {
    text-align: right;
}

.cashier-dash .table-order {
    border: 1px solid #ccc;
}

.cashier-dash .menu-cashier {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #0d6efd;
    color: #fff;
    padding: 12px 16px;
    gap: 8px;
    z-index: 1000;
}

.cashier-dash {
    padding-bottom: 130px;
}

.table-order th,
.table-order td {
    vertical-align: middle;
}

.table-order tbody tr td[contenteditable="true"] {
    outline: none;
    background: #fffdfa;
}

.table-order tbody tr td.editing {
    box-shadow: inset 0 0 0 2px #ffec99;
}

.total-payment h1 {
    font-weight: 700;
}

.menu-cashier .btn {
    min-width: 120px;
    margin-right: 8px;
}

@media (max-width: 576px) {
    .menu-cashier .btn {
        min-width: auto;
        margin: 4px;
    }
}

/* --- Enhanced visual design --- */
:root {
    --brand: #0d6efd;
    --bg: #f6f8fb;
    --text: #1f2937;
    --muted: #64748b;
    --surface: #ffffff;
    --border: #e5e7eb;
}

body,
.cashier-dash {
    background: var(--bg);
    color: var(--text);
}

.container-custom {
    max-width: 1280px;
    margin: 16px auto 30px;
    padding: 0 16px;
}

.card {
    border: none;
    border-radius: 12px;
    background: var(--surface);
    box-shadow: 0 4px 16px rgba(2, 6, 23, 0.06);
}

.total-payment h4 {
    color: var(--muted);
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.08em;
    margin-bottom: 4px;
}

.total-payment h1 {
    color: var(--brand);
    font-weight: 800;
    line-height: 1;
    font-size: clamp(28px, 4vw, 48px);
}

.table-order {
    margin-top: 24px;
    border: 1px solid var(--border);
    border-radius: 12px;
    overflow: hidden;
}

.table-order th,
.table-order td {
    padding: 0.9rem 1rem;
}

.table-order thead th {
    background: #f4f6fb;
    position: sticky;
    top: 0;
    z-index: 5;
}
.table-order thead.stuck th{
    box-shadow: 0 3px 10px rgba(2,6,23,.08);
}

.table-order tbody tr:nth-child(even) {
    background: #fafbfc;
}

.table-order tbody tr:hover {
    background: #eef2ff;
}

.table-order td:nth-child(1),
.table-order td:nth-child(5),
.table-order td:nth-child(6) {
    text-align: right;
}

.table-order td:nth-child(4) {
    text-align: center;
}

.table-order td:nth-child(2) {
    font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace;
    letter-spacing: 0.3px;
    color: #334155;
}

.table-order tbody tr td[contenteditable="true"] {
    background: #fffdfa;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.table-order tbody tr td.editing {
    box-shadow: inset 0 0 0 2px #ffec99, 0 0 0 9999px rgba(255, 255, 255, 0);
}

.menu-cashier {
    background: linear-gradient(90deg, #0d6efd, #0a58ca);
    padding-bottom: calc(12px + env(safe-area-inset-bottom));
    box-shadow: 0 -6px 24px rgba(2, 6, 23, 0.18);
    backdrop-filter: saturate(1.05) blur(3px);
}

.menu-cashier .p-2.flex-fill {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.menu-cashier .btn {
    border-radius: 999px;
    padding: 10px 16px;
    font-weight: 600;
}

.menu-cashier .btn-primary,
.menu-cashier .btn-outline-primary:hover {
    box-shadow: 0 8px 20px rgba(13,110,253, .35);
}

.menu-cashier .btn:hover {
    transform: translateY(-1px);
}

.menu-cashier .btn:active {
    transform: translateY(0);
}


/* Scan bar styling */
.scan-bar .input-group {
    border: 1px solid var(--border);
    border-radius: 12px;
    overflow: hidden;
}

.scan-bar .input-group-text {
    border: none;
    color: #475569;
}

.scan-bar .form-control {
    border: none;
    box-shadow: none;
}

.scan-bar .btn {
    border-radius: 0;
}


.scroll-table {
    overflow-y: auto;
    height: calc(100vh - 280px); /* adapt to viewport */
    margin-top: 10px;
    border-radius: 12px;
    background: #ffffff;
    border: 1px solid var(--border);
}


body {
    overflow: hidden;
    height: 100vh;
}

.head-cashier .img-fluid{
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid #000000;
}

/* POS-like bars */
.pos-menubar{
  position: sticky; top: 0; z-index: 1100;
  display:flex; gap:16px; align-items:center;
  background: linear-gradient(#1f4aa8,#183b85);
  color:#eaf2ff; padding:8px 16px; border-bottom:1px solid rgba(255,255,255,.15);
  font-weight:600; letter-spacing:.3px;
}
.pos-menubar span{cursor:default; opacity:.95}

.pos-statusbar{
  position: sticky; bottom: calc(100px + 0px); /* above bottom menu */
  display:flex; justify-content:space-between; gap:16px; align-items:center;
  background:#eef2ff; color:#1e293b; border-top:1px solid #c7d2fe;
  padding:6px 16px; font-size:.9rem;
}
.pos-statusbar .right{display:flex; gap:12px;}

/* POS table skin */
.pos-table-container{ margin-top: 8px; border:1px solid var(--border); border-radius:10px; overflow:hidden; background: #fff; }
.pos-table{ width:100%; border-collapse:separate; border-spacing:0; }
.pos-table thead th{ background: #1f4aa8; color:#fff; padding:.7rem .8rem; font-weight:700; }
.pos-table tbody td{ padding:.65rem .8rem; }
.pos-table tbody tr:nth-child(even){ background:#f8fafc; }
.pos-table tbody tr:hover{ background:#eef2ff; }
.pos-table td:nth-child(1), .pos-table td:nth-child(5), .pos-table td:nth-child(6), .pos-table td:nth-child(7){ text-align:right; }
.pos-table td:nth-child(4){ text-align:center; }

/* Keep existing bootstrap table styles working */
.table.table-order{ border:none; margin:0; }
.scroll-table{ height: calc(100vh - 280px); margin-top: 10px; }

/* Adapt header block to look app-like */
.head-cashier .card{ background:#eef4ff; border:1px solid #c7d2fe; border-radius: 12px; }
.head-cashier .card-body{ padding: 10px 12px; }
.head-cashier .img-fluid{ width: 72px; height: 72px; border-radius: 8px; object-fit: cover; }
.head-cashier h5{ margin: 2px 0; font-weight: 700; }
.total-payment h4{ color:#475569; font-weight:700; letter-spacing:.06em; text-transform:uppercase; }
.total-payment h1{ color:#0b2f97; font-size: clamp(32px, 5vw, 56px); font-weight:800; line-height:1; }

