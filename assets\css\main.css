.container-custom {
  margin: 10px 30px;
}
.container-custom .img-fluid {
  width: 100px;
}
.container-custom .ml-auto {
  margin-left: auto;
}
.container-custom .mr-auto {
  margin-right: auto;
}

.cashier-dash .total-payment {
  text-align: right;
}
.cashier-dash .table-order {
  margin-top: 40px;
  border: 1px solid #ccc;
}
.cashier-dash .menu-cashier {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #0d6efd;
  color: #fff;
  padding: 12px 16px;
  gap: 8px;
  z-index: 1000;
}

.cashier-dash {
  padding-bottom: 130px; /* prevent content hidden behind fixed menu */
}

.table-order th, .table-order td {
  vertical-align: middle;
}

.table-order tbody tr td[contenteditable="true"] {
  outline: none;
  background: #fffdfa;
}

.table-order tbody tr td.editing {
  box-shadow: inset 0 0 0 2px #ffec99;
}

.total-payment h1 {
  font-weight: 700;
}

.menu-cashier .btn {
  min-width: 120px;
  margin-right: 8px;
}

@media (max-width: 576px) {
  .menu-cashier .btn {
    min-width: auto;
    margin: 4px;
  }
}